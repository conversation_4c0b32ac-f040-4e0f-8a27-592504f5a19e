#!/usr/bin/env python3
"""
Test the complete /ask pipeline lifecycle
From Discord command → Intent Detection → Data Fetching → AI Response → Final Output
"""

import asyncio
import sys
import os
sys.path.insert(0, 'src')

async def test_complete_pipeline():
    """Test the entire pipeline from start to finish"""
    print("🚀 Testing Complete /ask Pipeline Lifecycle")
    print("=" * 70)
    
    # Test queries covering different scenarios
    test_cases = [
        {
            "name": "Support/Resistance Analysis",
            "query": "What are the support and resistance levels for AAPL?",
            "expected_intent": "support_resistance",
            "expected_symbols": ["AAPL"]
        },
        {
            "name": "Technical Analysis",
            "query": "What is the RSI and MACD for MSFT?",
            "expected_intent": "technical_analysis", 
            "expected_symbols": ["MSFT"]
        },
        {
            "name": "Price Check",
            "query": "How is NVDA doing today?",
            "expected_intent": "price_check",
            "expected_symbols": ["NVDA"]
        },
        {
            "name": "Comparison",
            "query": "Compare AAPL vs GOOGL performance",
            "expected_intent": "comparison",
            "expected_symbols": ["AAPL", "GOOGL"]
        },
        {
            "name": "Recommendation",
            "query": "Should I buy TSLA right now?",
            "expected_intent": "recommendation",
            "expected_symbols": ["TSLA"]
        }
    ]
    
    # Import required modules
    from shared.ai_services.enhanced_intent_detector import EnhancedIntentDetector
    from shared.ai_chat.ai_client import AIClientWrapper
    from shared.data_providers.aggregator import DataProviderAggregator
    from shared.technical_analysis.enhanced_calculator import EnhancedTechnicalCalculator
    from shared.ai_services.anti_hallucination_prompt import get_anti_hallucination_prompt
    from shared.ai_services.fact_verifier import TradingFactVerifier
    
    print("📋 Initializing Pipeline Components...")
    
    # Initialize components
    intent_detector = EnhancedIntentDetector()
    ai_client = AIClientWrapper()
    data_aggregator = DataProviderAggregator()
    tech_calculator = EnhancedTechnicalCalculator()
    fact_verifier = TradingFactVerifier()
    
    print("✅ All components initialized")
    print()
    
    # Test each case
    for i, test_case in enumerate(test_cases, 1):
        print(f"🧪 TEST CASE {i}/{len(test_cases)}: {test_case['name']}")
        print("=" * 50)
        print(f"Query: {test_case['query']}")
        print("-" * 50)
        
        try:
            # STEP 1: Intent Detection
            print("1️⃣ Intent Detection...")
            analysis = await intent_detector.analyze_intent(test_case['query'])
            
            print(f"   ✅ Intent: {analysis.primary_intent.value}")
            print(f"   ✅ Symbols: {analysis.entities.get('symbols', [])}")
            print(f"   ✅ Confidence: {analysis.confidence:.2f}")
            print(f"   ✅ Method: {analysis.method}")
            
            # Validate intent detection
            if analysis.primary_intent.value == test_case['expected_intent']:
                print("   🎯 Intent detection: CORRECT")
            else:
                print(f"   ⚠️ Intent detection: Expected {test_case['expected_intent']}, got {analysis.primary_intent.value}")
            
            # Validate symbol extraction
            extracted_symbols = analysis.entities.get('symbols', [])
            if set(extracted_symbols) == set(test_case['expected_symbols']):
                print("   🎯 Symbol extraction: CORRECT")
            else:
                print(f"   ⚠️ Symbol extraction: Expected {test_case['expected_symbols']}, got {extracted_symbols}")
            
            # STEP 2: Data Fetching (if symbols present)
            if extracted_symbols:
                print("\n2️⃣ Data Fetching...")
                data_results = {}
                
                for symbol in extracted_symbols:
                    try:
                        print(f"   📊 Fetching data for {symbol}...")
                        # Try to get current price data
                        price_data = await data_aggregator.get_ticker(symbol)
                        if price_data:
                            data_results[symbol] = price_data
                            print(f"   ✅ {symbol}: ${price_data.get('price', 'N/A')}")
                        else:
                            print(f"   ⚠️ {symbol}: No price data available")
                    except Exception as e:
                        print(f"   ❌ {symbol}: Error - {e}")
                        data_results[symbol] = None
            else:
                print("\n2️⃣ Data Fetching... SKIPPED (no symbols)")
                data_results = {}
            
            # STEP 3: Technical Analysis (if applicable)
            # Expand technical analysis to more intent types for better user experience
            should_run_technical = (
                analysis.primary_intent.value in [
                    'technical_analysis', 'support_resistance', 'support_resistance_levels',
                    'recommendation', 'comparison'  # Added these for comprehensive analysis
                ] and extracted_symbols
            )

            if should_run_technical:
                print("\n3️⃣ Technical Analysis...")
                tech_results = {}
                
                for symbol in extracted_symbols:
                    try:
                        print(f"   📈 Calculating indicators for {symbol}...")
                        # Get historical data for technical analysis
                        hist_data = await data_aggregator.get_history(symbol, period="1mo")
                        if hist_data and hist_data.get('success') and hist_data.get('data'):
                            # Use the enhanced calculator with HistoricalData object
                            indicators_result = await tech_calculator.calculate_indicators_from_historical_data(hist_data['data'])
                            if indicators_result.indicators:
                                tech_results[symbol] = indicators_result.indicators
                                print(f"   ✅ {symbol}: {len(indicators_result.indicators)} indicators calculated")
                            else:
                                print(f"   ⚠️ {symbol}: No indicators calculated - {indicators_result.metadata.get('error', 'Unknown error')}")
                        else:
                            print(f"   ⚠️ {symbol}: Insufficient data for technical analysis")
                    except Exception as e:
                        print(f"   ❌ {symbol}: Technical analysis error - {e}")
                        tech_results[symbol] = None
            else:
                print("\n3️⃣ Technical Analysis... SKIPPED")
                tech_results = {}
            
            # STEP 4: AI Response Generation
            print("\n4️⃣ AI Response Generation...")
            
            # Build context for AI
            context = {
                "query": test_case['query'],
                "intent": analysis.primary_intent.value,
                "symbols": extracted_symbols,
                "confidence": analysis.confidence,
                "price_data": data_results,
                "technical_data": tech_results
            }
            
            # Create anti-hallucination prompt with real data context
            context = {
                'query': test_case['query'],
                'intent': analysis.primary_intent.value,
                'symbols': extracted_symbols,
                'confidence': analysis.confidence,
                'price_data': data_results,
                'technical_data': tech_results
            }
            
            prompt = get_anti_hallucination_prompt(context)
            
            # Generate AI response
            ai_response = await ai_client.generate_response(prompt)
            
            if ai_response:
                print(f"   ✅ AI Response Generated ({len(ai_response)} chars)")
                print(f"   📝 Response Preview: {ai_response[:200]}...")
                
                # STEP 5: Fact Verification (NEW SAFETY CHECKPOINT)
                print("\n5️⃣ Fact Verification...")
                fact_check = await fact_verifier.verify_response(
                    ai_response=ai_response,
                    real_data=data_results,
                    symbols=extracted_symbols,
                    intent=analysis.primary_intent.value
                )
                
                print(f"   🔍 Fact Check: {'✅ VALID' if fact_check.is_valid else '❌ INVALID'}")
                print(f"   📊 Confidence: {fact_check.confidence:.2f}")
                print(f"   🚨 Issues Found: {len(fact_check.issues_found)}")
                
                if fact_check.issues_found:
                    print(f"   ⚠️ Issues: {fact_check.issues_found[:3]}...")  # Show first 3 issues
                
                # Use sanitized response if available
                final_response = fact_check.sanitized_response if fact_check.sanitized_response else ai_response
                
                # Quality assessment with fact verification
                quality_indicators = [
                    'support', 'resistance', 'price', 'technical', 'analysis',
                    'indicator', 'trading', 'market', 'recommendation', 'risk'
                ]
                quality_score = sum(1 for indicator in quality_indicators 
                                  if indicator.lower() in final_response.lower())
                
                # Improved anti-hallucination scoring
                positive_indicators = [
                    'current price', 'real-time data', 'based on data',
                    'according to data', 'data shows', 'market data indicates'
                ]
                negative_indicators = [
                    'no real data', 'data not available', 'missing data',
                    'cannot provide', 'unable to', 'requires real data',
                    'technical difficulties', 'try again'
                ]

                positive_score = sum(1 for indicator in positive_indicators
                                   if indicator.lower() in final_response.lower())
                negative_score = sum(1 for indicator in negative_indicators
                                   if indicator.lower() in final_response.lower())

                # Calculate anti-hallucination score (higher is better)
                anti_hallucination_score = max(0, positive_score - negative_score)
                
                # Check for fake data indicators
                fake_data_indicators = [
                    '$194.78', 'july 10, 2024', 'july 31', 'q3 earnings',
                    'specific price', 'exact level', 'precise value'
                ]
                fake_data_detected = any(indicator.lower() in final_response.lower() 
                                       for indicator in fake_data_indicators)
                
                print(f"   📊 Quality Score: {quality_score}/{len(quality_indicators)}")
                print(f"   🛡️ Anti-Hallucination Score: {anti_hallucination_score}/6")
                print(f"   🔒 Fact Verification: {'PASSED' if fact_check.is_valid else 'FAILED'}")
                
                if fake_data_detected:
                    print("   🚨 HALLUCINATION DETECTED: AI is making up fake data!")
                elif not fact_check.is_valid:
                    print("   ⚠️ FACT CHECK FAILED: Response contains unverified claims")
                elif anti_hallucination_score >= 2:
                    print("   ✅ GOOD: AI properly acknowledges data limitations")
                elif quality_score >= 7:
                    print("   🎉 EXCELLENT: High-quality trading response!")
                elif quality_score >= 5:
                    print("   ✅ GOOD: Decent trading response")
                else:
                    print("   ⚠️ POOR: Response lacks trading-specific content")
            else:
                print("   ❌ AI Response Generation: FAILED")
            
            # STEP 6: Pipeline Summary
            print(f"\n6️⃣ Pipeline Summary for '{test_case['name']}':")
            print(f"   Intent Detection: {'✅' if analysis.primary_intent.value == test_case['expected_intent'] else '⚠️'}")
            print(f"   Symbol Extraction: {'✅' if set(extracted_symbols) == set(test_case['expected_symbols']) else '⚠️'}")
            print(f"   Data Fetching: {'✅' if data_results else '⚠️'}")
            print(f"   Technical Analysis: {'✅' if tech_results else '⚠️'}")
            print(f"   AI Response: {'✅' if ai_response else '❌'}")
            print(f"   Fact Verification: {'✅' if ai_response and fact_check.is_valid else '⚠️' if ai_response else '❌'}")
            
        except Exception as e:
            print(f"❌ Pipeline Error: {e}")
            import traceback
            traceback.print_exc()
        
        print("\n" + "="*70 + "\n")
    
    print("🏁 Complete Pipeline Lifecycle Test Finished!")
    print("=" * 70)

if __name__ == "__main__":
    asyncio.run(test_complete_pipeline())
