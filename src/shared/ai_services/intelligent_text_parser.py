"""
Intelligent Text Parser Service

This service replaces rigid regex patterns with AI-powered text processing
for more flexible and intelligent parsing of financial text.
"""

import asyncio
import logging
import re
import time
from typing import Dict, List, Any, Optional, Tuple, Union
from dataclasses import dataclass
from enum import Enum

from src.shared.error_handling.logging import get_logger
from src.shared.ai_chat.ai_client import AIClientWrapper

logger = get_logger(__name__)

class ParseType(Enum):
    """Types of parsing operations"""
    SYMBOL_EXTRACTION = "symbol_extraction"
    PRICE_EXTRACTION = "price_extraction"
    PERCENTAGE_EXTRACTION = "percentage_extraction"
    INTENT_DETECTION = "intent_detection"
    SENTIMENT_ANALYSIS = "sentiment_analysis"
    NUMERICAL_EXTRACTION = "numerical_extraction"
    DATE_EXTRACTION = "date_extraction"
    ENTITY_EXTRACTION = "entity_extraction"

@dataclass
class ParseResult:
    """Result of AI-powered parsing"""
    parse_type: ParseType
    extracted_data: List[Dict[str, Any]]
    confidence: float
    raw_text: str
    processing_time: float
    fallback_used: bool = False

class IntelligentTextParser:
    """
    AI-powered text parser that replaces rigid regex patterns with
    intelligent natural language processing
    """
    
    def __init__(self):
        self.ai_client = AIClientWrapper()
        
        # Fallback regex patterns for when AI fails
        self.fallback_patterns = {
            ParseType.SYMBOL_EXTRACTION: [
                r'\$([A-Z]{1,5})\b',
                r'\b([A-Z]{2,5})\b',
                r'([A-Z]{1,5})\s+stock',
                r'ticker\s+([A-Z]{1,5})'
            ],
            ParseType.PRICE_EXTRACTION: [
                r'\$(\d+(?:\.\d{2})?)',
                r'(\d+(?:\.\d+)?)\s*(?:dollars?|USD)',
                r'price.*?(\d+(?:\.\d{2})?)',
                r'(\d+(?:\.\d+)?)\s*%'
            ],
            ParseType.NUMERICAL_EXTRACTION: [
                r'(\d+(?:\.\d+)?)\s*%',
                r'(\d+(?:\.\d+)?)\s*(?:million|billion|trillion)',
                r'(\d+(?:\.\d+)?)\s*(?:K|M|B|T)'
            ]
        }
        
        # AI prompts for different parsing tasks
        self.ai_prompts = {
            ParseType.SYMBOL_EXTRACTION: """
    Extract all stock symbols, tickers, and company identifiers from the following text.
    Return a JSON list of objects with 'symbol', 'confidence', and 'context' fields.
    Only include valid stock symbols (1-5 uppercase letters).
    
    Text: "{text}"
    
    Response format:
    [{{"symbol": "AAPL", "confidence": 0.95, "context": "Apple stock"}}]
    """,
            ParseType.PRICE_EXTRACTION: """
    Extract all price values, monetary amounts, and financial figures from the following text.
    Return a JSON list of objects with 'value', 'currency', 'confidence', and 'context' fields.
    
    Text: "{text}"
    
    Response format:
    [{{"value": 150.25, "currency": "USD", "confidence": 0.9, "context": "stock price"}}]
    """,
            ParseType.PERCENTAGE_EXTRACTION: """
    Extract all percentage values, growth rates, changes, and proportional figures from the following text.
    Focus specifically on percentages (e.g., 5.2%, increased by 10%, etc.).
    Return a JSON list of objects with 'value', 'unit', 'confidence', 'context', and 'direction' (if applicable, e.g., 'positive' or 'negative') fields.
    
    Text: "{text}"
    
    Response format:
    [{{"value": 5.2, "unit": "%", "confidence": 0.95, "context": "year-over-year growth", "direction": "positive"}}]
    """,
            ParseType.INTENT_DETECTION: """
    Analyze the intent and purpose of the following financial query.
    Return a JSON object with 'intent', 'confidence', 'entities', and 'action' fields.
    
    Text: "{text}"
    
    Response format:
    {{"intent": "price_inquiry", "confidence": 0.85, "entities": ["AAPL"], "action": "get_current_price"}}
    """,
            ParseType.SENTIMENT_ANALYSIS: """
    Analyze the sentiment and emotional tone of the following financial text.
    Return a JSON object with 'sentiment', 'confidence', 'emotions', and 'reasoning' fields.
    
    Text: "{text}"
    
    Response format:
    {{"sentiment": "bullish", "confidence": 0.8, "emotions": ["optimistic"], "reasoning": "positive language about growth"}}
    """,
            ParseType.NUMERICAL_EXTRACTION: """
    Extract all numerical values, percentages, ratios, and quantitative data from the following text.
    Return a JSON list of objects with 'value', 'type', 'unit', 'confidence', and 'context' fields.
    
    Text: "{text}"
    
    Response format:
    [{{"value": 15.5, "type": "percentage", "unit": "%", "confidence": 0.9, "context": "growth rate"}}]
    """
        }
    
    async def parse_text(self, text: str, parse_type: ParseType, 
                        use_ai: bool = True, fallback_on_failure: bool = True) -> ParseResult:
        """
        Parse text using AI-powered processing with regex fallback
        
        Args:
            text: Text to parse
            parse_type: Type of parsing to perform
            use_ai: Whether to use AI processing (True) or go straight to regex (False)
            fallback_on_failure: Whether to use regex fallback if AI fails
            
        Returns:
            ParseResult with extracted data
        """
        start_time = asyncio.get_event_loop().time()
        
        if use_ai:
            try:
                # Try AI-powered parsing first
                result = await self._ai_parse(text, parse_type)
                if result and result.extracted_data:
                    processing_time = asyncio.get_event_loop().time() - start_time
                    result.processing_time = processing_time
                    return result
                
                logger.debug(f"AI parsing returned no results for {parse_type.value}")
                
            except Exception as e:
                logger.warning(f"AI parsing failed for {parse_type.value}: {e}")
        
        # Use regex fallback
        if fallback_on_failure or not use_ai:
            result = await self._regex_fallback(text, parse_type)
            result.fallback_used = True
            processing_time = asyncio.get_event_loop().time() - start_time
            result.processing_time = processing_time
            return result
        
        # Return empty result if both AI and fallback disabled
        processing_time = asyncio.get_event_loop().time() - start_time
        return ParseResult(
            parse_type=parse_type,
            extracted_data=[],
            confidence=0.0,
            raw_text=text,
            processing_time=processing_time,
            fallback_used=False
        )
    
    async def _ai_parse(self, text: str, parse_type: ParseType) -> ParseResult:
        """Perform AI-powered parsing"""
        if parse_type not in self.ai_prompts:
            raise ValueError(f"No AI prompt defined for {parse_type.value}")
        
        prompt = self.ai_prompts[parse_type].format(text=text)
        
        try:
            # Use AI client for parsing tasks
            response = await self.ai_client.generate_response(prompt)

            if not response:
                logger.debug(f"AI client returned empty response for {parse_type.value}")
                return None

            logger.debug(f"Raw AI response for {parse_type.value}: {repr(response[:500])}...")  # Log first 500 chars for debugging

            # Extract JSON from AI response
            extracted_data = self._extract_json_from_response(response)
            
            if not extracted_data:
                logger.warning(f"No valid JSON extracted from AI response for {parse_type.value}")
                return None
            
            # Calculate confidence based on AI response quality
            confidence = self._calculate_ai_confidence(extracted_data, text)
            
            logger.debug(f"AI parsing successful for {parse_type.value}: {len(extracted_data) if isinstance(extracted_data, list) else 1} items extracted, confidence: {confidence:.2f}")
            
            return ParseResult(
                parse_type=parse_type,
                extracted_data=extracted_data if isinstance(extracted_data, list) else [extracted_data],
                confidence=confidence,
                raw_text=text,
                processing_time=0.0  # Will be set by caller
            )
            
        except Exception as e:
            logger.error(f"AI parsing error for {parse_type.value}: {e}")
            return None
    
    async def _regex_fallback(self, text: str, parse_type: ParseType) -> ParseResult:
        """Fallback to regex-based parsing"""
        extracted_data = []
        
        if parse_type in self.fallback_patterns:
            patterns = self.fallback_patterns[parse_type]
            
            for pattern in patterns:
                matches = re.finditer(pattern, text, re.IGNORECASE)
                for match in matches:
                    if parse_type == ParseType.SYMBOL_EXTRACTION:
                        symbol = match.group(1) if match.groups() else match.group(0)
                        extracted_data.append({
                            "symbol": symbol.upper(),
                            "confidence": 0.7,  # Lower confidence for regex
                            "context": match.group(0),
                            "position": match.span()
                        })
                    
                    elif parse_type == ParseType.PRICE_EXTRACTION:
                        value = match.group(1) if match.groups() else match.group(0)
                        try:
                            numeric_value = float(value.replace('$', '').replace(',', ''))
                            extracted_data.append({
                                "value": numeric_value,
                                "currency": "USD" if '$' in match.group(0) else "unknown",
                                "confidence": 0.6,
                                "context": match.group(0),
                                "position": match.span()
                            })
                        except ValueError:
                            continue
                    
                    elif parse_type == ParseType.NUMERICAL_EXTRACTION:
                        value = match.group(1) if match.groups() else match.group(0)
                        try:
                            numeric_value = float(value)
                            unit = "%" if "%" in match.group(0) else "number"
                            extracted_data.append({
                                "value": numeric_value,
                                "type": "percentage" if unit == "%" else "number",
                                "unit": unit,
                                "confidence": 0.6,
                                "context": match.group(0),
                                "position": match.span()
                            })
                        except ValueError:
                            continue
        
        # Remove duplicates and sort by confidence
        extracted_data = self._deduplicate_results(extracted_data)
        
        # Calculate overall confidence
        confidence = sum(item.get('confidence', 0) for item in extracted_data) / len(extracted_data) if extracted_data else 0.0
        
        return ParseResult(
            parse_type=parse_type,
            extracted_data=extracted_data,
            confidence=confidence,
            raw_text=text,
            processing_time=0.0  # Will be set by caller
        )
    
    def _extract_json_from_response(self, response: str) -> Union[List[Dict], Dict, None]:
        """Extract JSON data from AI response with enhanced robustness"""
        import json
        
        logger.debug(f"🔍 [PARSER-DEBUG] Starting JSON extraction timing")
        start_time = time.time()
        logger.info(f"🔍 [PARSER] Starting JSON extraction (response length: {len(response)})")
        
        # Log raw response preview for debugging
        logger.debug(f"🔍 [PARSER] Raw response preview: {repr(response[:300])}...")
        
        # Enhanced cleaning: Remove common AI response wrappers
        # Handle formats like: "Here is the JSON: {...}", "The result is: [...]", etc.
        response = response.strip()
        
        # Remove common prefixes/suffixes
        wrappers = [
            r'^.*?JSON:\s*',
            r'^.*?result:\s*',
            r'^.*?data:\s*',
            r'^.*?here is:\s*',
            r'\s*```json\s*',
            r'\s*```\s*$',
            r'\s*\[.*?\]\s*$',
            r'^\s*\{.*?\}\s*'
        ]
        for wrapper in wrappers:
            response = re.sub(wrapper, '', response, flags=re.DOTALL | re.IGNORECASE)
        
        # Strip markdown fences more aggressively
        lines = response.split('\n')
        cleaned_lines = []
        in_fence = False
        for line in lines:
            stripped = line.strip()
            if '```json' in stripped:
                in_fence = True
                continue
            if in_fence and '```' in stripped:
                in_fence = False
                continue
            if in_fence or not in_fence:
                cleaned_lines.append(line)
        
        cleaned_response = '\n'.join(cleaned_lines).strip()
        
        if not cleaned_response:
            cleaned_response = response
        
        logger.debug(f"🔍 [PARSER] Cleaned response preview: {repr(cleaned_response[:300])}...")
        
        # Enhanced JSON extraction patterns
        json_patterns = [
            r'\{[^{}]*(?:\{[^{}]*\}[^{}]*)*\}',  # Nested objects
            r'\[[^\[\]]*(?:\[[^\[\]]*\][^\[\]]*)?\]',  # Nested arrays
            r'\{.*?\}(?=\s*(?:,|\]|}|$))',  # Trailing object
            r'\[.*?\](?=\s*(?:,|\]|}|$))'   # Trailing array
        ]
        
        for pattern in json_patterns:
            matches = re.findall(pattern, cleaned_response, re.DOTALL)
            for match in matches:
                try:
                    # Clean the match further
                    match_clean = re.sub(r'^\s*//.*', '', match)  # Remove comments
                    parsed = json.loads(match_clean)
                    duration = time.time() - start_time
                    logger.debug(f"🔍 [PARSER-DEBUG] Pattern match successful after {duration:.2f}s")
                    logger.info(f"✅ [PARSER] JSON extracted successfully in {duration:.2f}s (type: {type(parsed)})")
                    return parsed
                except json.JSONDecodeError as e:
                    logger.debug(f"🔍 [PARSER] Pattern match failed: {e} (match preview: {repr(match[:100])}...)")
                    continue
        
        # Try parsing the entire cleaned response
        try:
            logger.debug(f"🔍 [PARSER-DEBUG] Attempting full response parse")
            parsed = json.loads(cleaned_response)
            duration = time.time() - start_time
            logger.debug(f"🔍 [PARSER-DEBUG] Full parse successful after {duration:.2f}s")
            logger.info(f"✅ [PARSER] Full response parsed as JSON in {duration:.2f}s (type: {type(parsed)})")
            return parsed
        except json.JSONDecodeError as e:
            duration = time.time() - start_time
            logger.debug(f"🔍 [PARSER-DEBUG] Full parse failed after {duration:.2f}s: {e}")
            logger.warning(f"❌ [PARSER] All JSON parsing failed after {duration:.2f}s: {e}")
            logger.debug(f"❌ [PARSER] Failed full response: {repr(cleaned_response[:500])}...")
            pass
        
        # Final fallback: Extract potential key-value pairs if no structured JSON
        logger.warning(f"⚠️ [PARSER] No structured JSON found - attempting simple extraction")
        logger.debug(f"🔍 [PARSER-DEBUG] Attempting simple key-value extraction")
        simple_data = self._simple_key_value_extract(cleaned_response)
        if simple_data:
            duration = time.time() - start_time
            logger.debug(f"🔍 [PARSER-DEBUG] Simple extraction successful after {duration:.2f}s: {len(simple_data)} items")
            logger.info(f"✅ [PARSER] Simple extraction succeeded in {duration:.2f}s: {len(simple_data)} items")
            return simple_data
        
        duration = time.time() - start_time
        logger.debug(f"🔍 [PARSER-DEBUG] Complete extraction failed after {duration:.2f}s")
        logger.error(f"❌ [PARSER] Complete extraction failure after {duration:.2f}s")
        return None
    
    def _simple_key_value_extract(self, text: str) -> Dict[str, Any]:
        """Fallback extraction for key-value pairs when JSON fails"""
        # Simple heuristic for common formats like "symbol: AAPL, confidence: 0.95"
        pairs = {}
        lines = text.split('\n')
        for line in lines:
            if ':' in line:
                key, value = line.split(':', 1)
                key = key.strip().lower()
                value = value.strip()
                if key in ['symbol', 'value', 'confidence', 'intent']:
                    try:
                        if key == 'symbols' or key == 'value':
                            pairs[key] = [value] if isinstance(value, str) else value
                        else:
                            pairs[key] = float(value) if '.' in value else value
                    except ValueError:
                        pairs[key] = value
        return pairs if pairs else None
    
    def _calculate_ai_confidence(self, extracted_data: Union[List[Dict], Dict], original_text: str) -> float:
        """Calculate confidence score for AI-extracted data"""
        if not extracted_data:
            return 0.0
        
        data_list = extracted_data if isinstance(extracted_data, list) else [extracted_data]
        
        # Base confidence from AI response
        base_confidence = 0.8
        
        # Adjust based on data quality
        for item in data_list:
            if isinstance(item, dict):
                # Check if confidence is provided by AI
                if 'confidence' in item:
                    try:
                        ai_confidence = float(item['confidence'])
                        base_confidence = (base_confidence + ai_confidence) / 2
                    except (ValueError, TypeError):
                        pass
                
                # Boost confidence if context is provided
                if 'context' in item and item['context']:
                    base_confidence += 0.05
                
                # Boost confidence if value seems reasonable
                if 'value' in item:
                    try:
                        value = float(item['value'])
                        if 0 < value < 10000:  # Reasonable range for most financial values
                            base_confidence += 0.05
                    except (ValueError, TypeError):
                        pass
        
        return min(1.0, base_confidence)
    
    def _deduplicate_results(self, results: List[Dict]) -> List[Dict]:
        """Remove duplicate results and keep highest confidence"""
        if not results:
            return results
        
        # Group by key field (symbol, value, etc.)
        groups = {}
        for result in results:
            key = None
            if 'symbol' in result:
                key = result['symbol']
            elif 'value' in result:
                key = str(result['value'])
            elif 'intent' in result:
                key = result['intent']
            
            if key:
                if key not in groups or result.get('confidence', 0) > groups[key].get('confidence', 0):
                    groups[key] = result
        
        return list(groups.values())
    
    # Convenience methods for common parsing tasks
    async def extract_symbols(self, text: str, use_ai: bool = True) -> List[str]:
        """Extract stock symbols from text"""
        result = await self.parse_text(text, ParseType.SYMBOL_EXTRACTION, use_ai)
        return [item.get('symbol', '') for item in result.extracted_data if 'symbol' in item]
    
    async def extract_prices(self, text: str, use_ai: bool = True) -> List[float]:
        """Extract price values from text"""
        result = await self.parse_text(text, ParseType.PRICE_EXTRACTION, use_ai)
        return [item.get('value', 0.0) for item in result.extracted_data if 'value' in item]
    
    async def extract_percentages(self, text: str, use_ai: bool = True) -> List[Dict[str, Any]]:
        """Extract percentage values from text using AI-only processing"""
        # Force AI usage, no fallback
        result = await self.parse_text(text, ParseType.PERCENTAGE_EXTRACTION, use_ai=True, fallback_on_failure=False)
        return result.extracted_data
    
    async def detect_intent(self, text: str, use_ai: bool = True) -> Optional[str]:
        """Detect the intent of the text"""
        result = await self.parse_text(text, ParseType.INTENT_DETECTION, use_ai)
        if result.extracted_data and isinstance(result.extracted_data[0], dict):
            return result.extracted_data[0].get('intent')
        return None
    
    async def analyze_sentiment(self, text: str, use_ai: bool = True) -> Optional[str]:
        """Analyze sentiment of the text"""
        result = await self.parse_text(text, ParseType.SENTIMENT_ANALYSIS, use_ai)
        if result.extracted_data and isinstance(result.extracted_data[0], dict):
            return result.extracted_data[0].get('sentiment')
        return None
    
    async def extract_numbers(self, text: str, use_ai: bool = True) -> List[Dict[str, Any]]:
        """Extract numerical values from text"""
        result = await self.parse_text(text, ParseType.NUMERICAL_EXTRACTION, use_ai)
        return result.extracted_data

# Global instance for easy access
intelligent_parser = IntelligentTextParser()
