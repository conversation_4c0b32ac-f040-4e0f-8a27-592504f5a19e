"""
Fact Verification Compartment for Trading AI
Validates all AI responses against real data to prevent hallucinations
"""

import re
import logging
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime, date

logger = logging.getLogger(__name__)

@dataclass
class FactCheckResult:
    """Result of fact verification"""
    is_valid: bool
    confidence: float
    issues_found: List[str]
    corrections_made: List[str]
    verified_data: Dict[str, Any]
    sanitized_response: str

class TradingFactVerifier:
    """
    Fact verification system that validates AI responses against real trading data
    """
    
    def __init__(self):
        self.price_pattern = re.compile(r'\$[\d,]+\.?\d*')
        self.date_pattern = re.compile(r'(january|february|march|april|may|june|july|august|september|october|november|december)\s+\d{1,2},?\s+\d{4}', re.IGNORECASE)
        self.percentage_pattern = re.compile(r'[\d,]+\.?\d*%')
        self.specific_price_pattern = re.compile(r'\$[\d,]+\.\d{2,}')
        
        # Common hallucination indicators
        self.hallucination_indicators = [
            'july 10, 2024', 'july 31', 'q3 earnings', 'earnings call',
            'conference call', 'analyst meeting', 'fed meeting',
            'specific price', 'exact level', 'precise value',
            'all-time high', '52-week high', 'recent high'
        ]
    
    async def verify_response(self, 
                            ai_response: str, 
                            real_data: Dict[str, Any],
                            symbols: List[str],
                            intent: str) -> FactCheckResult:
        """
        Verify AI response against real data
        
        Args:
            ai_response: The AI-generated response to verify
            real_data: Dictionary of real data (prices, indicators, etc.)
            symbols: List of symbols mentioned in the query
            intent: The detected intent
            
        Returns:
            FactCheckResult with verification details
        """
        logger.info(f"🔍 Starting fact verification for {len(symbols)} symbols")
        
        issues_found = []
        corrections_made = []
        verified_data = {}
        
        # Extract real price data
        real_prices = {}
        for symbol in symbols:
            if symbol in real_data and real_data[symbol]:
                price_data = real_data[symbol]
                if isinstance(price_data, dict) and 'price' in price_data:
                    real_prices[symbol] = price_data['price']
                    verified_data[f"{symbol}_price"] = price_data['price']
        
        # Check for price hallucinations
        price_issues = self._check_price_hallucinations(ai_response, real_prices, symbols)
        issues_found.extend(price_issues)
        
        # Check for date hallucinations
        date_issues = self._check_date_hallucinations(ai_response)
        issues_found.extend(date_issues)
        
        # Check for technical indicator hallucinations
        indicator_issues = self._check_indicator_hallucinations(ai_response, real_data)
        issues_found.extend(indicator_issues)
        
        # Check for general hallucination patterns
        general_issues = self._check_general_hallucinations(ai_response)
        issues_found.extend(general_issues)
        
        # Sanitize the response
        sanitized_response = self._sanitize_response(ai_response, real_prices, symbols)
        
        # Calculate confidence score
        confidence = self._calculate_confidence(issues_found, len(ai_response))
        
        # Determine if response is valid
        is_valid = len(issues_found) == 0 and confidence > 0.7
        
        result = FactCheckResult(
            is_valid=is_valid,
            confidence=confidence,
            issues_found=issues_found,
            corrections_made=corrections_made,
            verified_data=verified_data,
            sanitized_response=sanitized_response
        )
        
        logger.info(f"✅ Fact verification complete: {len(issues_found)} issues found, confidence: {confidence:.2f}")
        return result
    
    def _check_price_hallucinations(self, response: str, real_prices: Dict[str, float], symbols: List[str]) -> List[str]:
        """Check for price-related hallucinations"""
        issues = []
        
        # Find all price mentions in response
        price_matches = self.price_pattern.findall(response)
        
        for price_match in price_matches:
            # Extract numeric value
            price_value = float(price_match.replace('$', '').replace(',', ''))
            
            # Check if this price matches any real data
            price_found = False
            for symbol, real_price in real_prices.items():
                if abs(price_value - real_price) < 0.01:  # Allow small floating point differences
                    price_found = True
                    break
            
            if not price_found and symbols:
                issues.append(f"Potentially hallucinated price: {price_match} (no matching real data)")
        
        return issues
    
    def _check_date_hallucinations(self, response: str) -> List[str]:
        """Check for date-related hallucinations"""
        issues = []
        
        # Check for specific future dates that are likely fake
        future_dates = self.date_pattern.findall(response)
        current_year = datetime.now().year
        
        for date_str in future_dates:
            # Simple check for future dates (this could be more sophisticated)
            if str(current_year) in date_str or str(current_year + 1) in date_str:
                issues.append(f"Potentially hallucinated date: {date_str}")
        
        return issues
    
    def _check_indicator_hallucinations(self, response: str, real_data: Dict[str, Any]) -> List[str]:
        """Check for technical indicator hallucinations"""
        issues = []
        
        # Look for specific indicator values that might be hallucinated
        rsi_pattern = re.compile(r'RSI.*?(\d+(?:\.\d+)?)', re.IGNORECASE)
        macd_pattern = re.compile(r'MACD.*?([+-]?\d+(?:\.\d+)?)', re.IGNORECASE)
        
        # Check if response mentions specific indicator values without real data
        rsi_matches = rsi_pattern.findall(response)
        macd_matches = macd_pattern.findall(response)
        
        # If we have real technical data, verify against it
        has_real_indicators = any(
            symbol in real_data and 
            isinstance(real_data[symbol], dict) and 
            'indicators' in real_data[symbol]
            for symbol in real_data.keys()
        )
        
        if (rsi_matches or macd_matches) and not has_real_indicators:
            issues.append("Technical indicator values mentioned without real data backing")
        
        return issues
    
    def _check_general_hallucinations(self, response: str) -> List[str]:
        """Check for general hallucination patterns"""
        issues = []
        
        response_lower = response.lower()
        
        for indicator in self.hallucination_indicators:
            if indicator in response_lower:
                issues.append(f"Hallucination indicator detected: '{indicator}'")
        
        return issues
    
    def _sanitize_response(self, response: str, real_prices: Dict[str, float], symbols: List[str]) -> str:
        """Sanitize response by removing or correcting hallucinated data"""
        sanitized = response
        
        # Add disclaimer if no real data is available
        if not real_prices and symbols:
            disclaimer = "\n\n⚠️ **IMPORTANT**: This response is based on general market knowledge only. Real-time data is required for accurate trading decisions."
            sanitized += disclaimer
        
        # Replace specific hallucinated phrases
        replacements = {
            'july 10, 2024': 'recent trading session',
            'july 31': 'upcoming earnings period',
            'q3 earnings': 'upcoming earnings',
            'specific price': 'current market price',
            'exact level': 'approximate level',
            'precise value': 'approximate value'
        }
        
        for old, new in replacements.items():
            sanitized = re.sub(old, new, sanitized, flags=re.IGNORECASE)
        
        return sanitized
    
    def _calculate_confidence(self, issues: List[str], response_length: int) -> float:
        """Calculate confidence score based on issues found"""
        if not issues:
            return 1.0
        
        # Base confidence on number of issues relative to response length
        issue_density = len(issues) / max(response_length / 100, 1)
        
        # Convert to 0-1 scale
        confidence = max(0.0, 1.0 - min(issue_density, 1.0))
        
        return confidence

class ResponseSanitizer:
    """
    Additional layer for sanitizing AI responses
    """
    
    @staticmethod
    def add_data_disclaimer(response: str, has_real_data: bool) -> str:
        """Add appropriate disclaimers based on data availability"""
        if not has_real_data:
            return response + "\n\n⚠️ **Disclaimer**: This analysis is based on general market knowledge. Real-time data is required for trading decisions."
        return response
    
    @staticmethod
    def remove_specific_claims(response: str) -> str:
        """Remove overly specific claims that might be hallucinated"""
        # Remove very specific price claims
        response = re.sub(r'\$[\d,]+\.\d{2,}', 'current market price', response)
        
        # Remove specific date claims
        response = re.sub(r'(january|february|march|april|may|june|july|august|september|october|november|december)\s+\d{1,2},?\s+\d{4}', 'recent trading session', response, flags=re.IGNORECASE)
        
        return response
