"""
AI Client Wrapper for OpenRouter/OpenAI integration
"""

import logging
import json
import os
import asyncio
import re
from typing import Dict, Any, Optional, Union
from datetime import datetime
import time

# Import OpenRouter API key
try:
    from src.shared.ai_services.openrouter_key import OPENROUTER_API_KEY
except ImportError:
    OPENROUTER_API_KEY = ""

# Import timeout and circuit breaker managers
try:
    from src.shared.ai_services.timeout_manager import timeout_manager
    from src.shared.ai_services.circuit_breaker import ai_circuit_breaker_manager
except ImportError:
    # Fallback implementations if the modules don't exist yet
    class MockTimeoutManager:
        def timeout_wrapper(self, service_type: str = 'default'):
            def decorator(func):
                return func
            return decorator
    
    class MockCircuitBreakerManager:
        def get_breaker(self, service_name: str, config=None):
            class MockBreaker:
                async def can_execute(self):
                    return True
                async def success(self):
                    pass
                async def failure(self):
                    pass
            return MockBreaker()
    
    timeout_manager = MockTimeoutManager()
    ai_circuit_breaker_manager = MockCircuitBreakerManager()

from src.shared.error_handling.logging import get_logger
logger = get_logger(__name__)

from .config import Config
from .models import AIAskResult

def get_system_prompt():
    """Get system prompt with current date/time injection"""
    from datetime import datetime
    current_date = datetime.now().strftime("%B %d, %Y")
    current_time = datetime.now().strftime("%I:%M %p %Z")

    return f"""You are a financial analysis assistant. Analyze the user's query and respond with JSON.

CURRENT CONTEXT:
- Today's date: {current_date}
- Current time: {current_time}
- You are operating in 2025, NOT 2023 or earlier
- All market references should be current and relevant to today's date
- You have full knowledge of the current date and time

Your response must be valid JSON with these fields:
- intent: One of "stock_analysis", "market_overview", "price_check", "general_question"
- symbols: List of stock symbols mentioned (e.g., ["AAPL", "MSFT"])
- needs_data: Boolean indicating if you need market data
- response: Your response to the user's query

CRITICAL: NEVER ask for permission to pull data. When symbols are mentioned, automatically fetch data and provide complete analysis immediately.

Example: {{"intent": "stock_analysis", "symbols": ["AAPL"], "needs_data": true, "response": "Apple stock analysis with current data..."}}"""

FALLBACK_RESPONSES = {
    "no_ai_config": "I'm sorry, but I'm not able to process your request at the moment due to a configuration issue. Please try again later.",
    "ai_error": "I apologize, but I encountered an error while processing your request. Please try again with a different query."
}

class CircuitBreaker:
    """Simple circuit breaker implementation"""
    def __init__(self, failure_threshold=3, timeout=60):
        self.failure_threshold = failure_threshold
        self.timeout = timeout
        self.failure_count = 0
        self.last_failure_time = None
        self.state = "CLOSED"  # CLOSED, OPEN, HALF_OPEN
        
    async def can_execute(self):
        """Check if request can be executed"""
        if self.state == "OPEN":
            if self.last_failure_time and time.time() - self.last_failure_time >= self.timeout:
                self.state = "HALF_OPEN"
                return True
            return False
        return True
        
    async def success(self):
        """Record successful request"""
        self.failure_count = 0
        self.state = "CLOSED"
        
    async def failure(self):
        """Record failed request"""
        self.failure_count += 1
        self.last_failure_time = time.time()
        if self.failure_count >= self.failure_threshold:
            self.state = "OPEN"

class AIClientWrapper:
    async def _async_chat_completion(self, messages):
        """Helper method to run chat completion in a thread pool with proper error handling"""
        if not self.client:
            raise RuntimeError("AI client is not initialized")
        
        start_time = time.time()
        logger.info(f"🔄 [{self.pipeline_id}] Starting AI call to {self.model} (timeout: {self.ai_timeout}s)")
            
        try:
            # Use a thread pool executor to run the sync OpenAI client
            loop = asyncio.get_event_loop()
            
            # Create a task with timeout
            task = loop.run_in_executor(
                None,
                lambda: self.client.chat.completions.create(
                    model=self.model,
                    messages=messages,
                    temperature=self.temperature,
                    max_tokens=self.max_tokens,
                )
            )
            
            # Wait for the task with a timeout
            completion = await asyncio.wait_for(task, timeout=self.ai_timeout)
            
            duration = time.time() - start_time
            logger.info(f"✅ [{self.pipeline_id}] AI call completed in {duration:.2f}s")
            return completion
            
        except asyncio.TimeoutError:
            duration = time.time() - start_time
            logger.error(f"⏰ [{self.pipeline_id}] AI chat completion timed out after {self.ai_timeout}s (actual time: {duration:.2f}s)")
            raise
        except Exception as e:
            duration = time.time() - start_time
            logger.error(f"❌ [{self.pipeline_id}] Error in chat completion after {duration:.2f}s: {str(e)}", exc_info=True)
            raise
        
    def __init__(self, context: Optional[Any] = None):
        self.context = context
        self.pipeline_id = getattr(context, 'pipeline_id', 'unknown') if context else 'unknown'
        self.client = None  # Initialize client as None by default
        
        # Load configuration - Updated to use SmartModelRouter
        try:
            from src.shared.ai_services.smart_model_router import router
            self.model = router.get_model_for_query_analysis()  # Use SmartModelRouter for model selection
            self.router = router
        except ImportError:
            # Fallback to environment variables if SmartModelRouter not available
            self.model = Config.get('pipeline', 'model', os.getenv('MODEL_GLOBAL_FALLBACK', 'moonshotai/kimi-k2-0905'))
            self.router = None

        self.temperature = Config.get('pipeline', 'temperature', float(os.getenv('AI_DEFAULT_TEMPERATURE', '0.7')))
        self.max_tokens = Config.get('pipeline', 'max_tokens', int(os.getenv('AI_DEFAULT_MAX_TOKENS', '2000')))
        self.ai_timeout = Config.get('pipeline', 'timeout', float(os.getenv('AI_DEFAULT_TIMEOUT_MS', '30000')) / 1000.0)
        
        # Initialize circuit breaker first
        self.circuit_breaker = CircuitBreaker(failure_threshold=3, timeout=60)
        
        # Configure OpenRouter client
        self._initialize_openai_client()
    
    def _initialize_openai_client(self):
        """Initialize the OpenAI client with proper error handling"""
        api_key = OPENROUTER_API_KEY
        base_url = os.getenv('OPENROUTER_BASE_URL', 'https://openrouter.ai/api/v1')
        
        if not (api_key and base_url):
            logger.warning(f"❌ [{self.pipeline_id}] OpenRouter not configured - missing API key or base URL")
            self.client = None
            return
            
        if not (api_key.startswith('sk-') or api_key.startswith('sk-or-v1')):
            logger.error(f"❌ [{self.pipeline_id}] Invalid API key format")
            self.client = None
            return
            
        try:
            from openai import OpenAI
        except ImportError as import_e:
            logger.error(f"❌ [{self.pipeline_id}] Failed to import OpenAI: {import_e}")
            self.client = None
            return
            
        try:
            self.client = OpenAI(
                api_key=api_key,
                base_url=base_url,
                timeout=self.ai_timeout
            )
            logger.info(f"✅ [{self.pipeline_id}] OpenRouter client configured with model: {self.model}")
        except Exception as e:
            logger.error(f"❌ [{self.pipeline_id}] Failed to create OpenAI client: {e}")
            self.client = None

    async def classify_query(self, query: str) -> Dict[str, Any]:
        """
        Classify the query using AI to determine intent, symbols, etc.
        
        Args:
            query: The user's query string
            
        Returns:
            Dict with intent, symbols, needs_data, response
        """
        max_attempts = 3
        last_error = None
        
        # Validate input
        if not query or not isinstance(query, str):
            logger.warning(f"[{self.pipeline_id}] Invalid query: {query}")
            return AIAskResult(
                intent="general_question",
                symbols=[],
                needs_data=False,
                response="Please provide a valid query."
            ).dict()

        # Check circuit breaker
        if not await self.circuit_breaker.can_execute():
            logger.warning(f"[{self.pipeline_id}] Circuit breaker is OPEN. Rejecting AI call.")
            return AIAskResult(
                intent="general_question",
                symbols=[],
                needs_data=False,
                response="AI service is temporarily unavailable due to high error rates. Please try again in a few minutes."
            ).dict()

        for attempt in range(1, max_attempts + 1):
            try:
                if self.client is None:
                    logger.warning(f"[{self.pipeline_id}] AI client is not configured (missing API key). Returning fallback response.")
                    await self.circuit_breaker.failure()
                    return AIAskResult(
                        intent="general_question",
                        symbols=[],
                        needs_data=False,
                        response=FALLBACK_RESPONSES["no_ai_config"]
                    ).dict()

                messages = [
                    {"role": "system", "content": get_system_prompt()},
                    {"role": "user", "content": query},
                ]

                logger.info(f"🤖 [{self.pipeline_id}] Calling AI model {self.model} with query: {query[:50]}...")

                # Apply timeout to the AI call with proper async handling
                try:
                    # Create a task with timeout
                    completion_task = asyncio.create_task(
                        self._async_chat_completion(messages)
                    )
                    completion = await asyncio.wait_for(completion_task, timeout=self.ai_timeout)
                except asyncio.TimeoutError:
                    logger.error(f"⏰ [{self.pipeline_id}] AI call timed out after {self.ai_timeout} seconds")
                    await self.circuit_breaker.failure()
                    # Return a quick fallback response instead of hanging
                    return AIAskResult(
                        intent="general_question",
                        symbols=[],
                        needs_data=False,
                        response="⏰ The AI service took too long to respond. Please try a simpler query or try again later."
                    ).dict()

                # Parse the response
                response_text = completion.choices[0].message.content.strip()
                
                # Sanitize and parse the JSON response
                parsed = self._extract_json_from_response(response_text)
                if not parsed:
                    logger.error(f"❌ [{self.pipeline_id}] Failed to parse AI response.\nResponse: {response_text}")
                    last_error = "Invalid JSON response"
                    continue

                # Validate with model
                validated_result = AIAskResult.from_ai_response(parsed)
                
                # Check for placeholders
                if self._contains_placeholders(validated_result.response):
                    logger.warning(f"⚠️ [{self.pipeline_id}] AI response contains template placeholders - generating fallback response")
                    await self.circuit_breaker.failure()
                    return AIAskResult(
                        intent=validated_result.intent,
                        symbols=validated_result.symbols,
                        needs_data=validated_result.needs_data,
                        response="I need to fetch current market data to provide accurate analysis. Please try again in a moment."
                    ).dict()
                
                logger.info(f"✅ [{self.pipeline_id}] AI call successful. Intent: {validated_result.intent}, Symbols: {validated_result.symbols}")
                await self.circuit_breaker.success()
                return validated_result.dict()

            except Exception as e:
                last_error = str(e)
                logger.error(f"❌ [{self.pipeline_id}] AI call failed (attempt {attempt}/{max_attempts}): {last_error}", exc_info=True)
                await self.circuit_breaker.failure()
                
                if attempt < max_attempts:
                    await asyncio.sleep(2 ** attempt)  # Exponential backoff
                else:
                    # After max attempts, return error response instead of hanging
                    logger.error(f"❌ [{self.pipeline_id}] AI call failed after {max_attempts} attempts. Last error: {last_error}")
                    return AIAskResult(
                        intent="general_question",
                        symbols=[],
                        needs_data=False,
                        response="❌ I encountered repeated errors while processing your request. Please try again later or with a different query."
                    ).dict()

        # If all attempts fail
        logger.error(f"❌ [{self.pipeline_id}] AI call failed after {max_attempts} attempts. Last error: {last_error}")
        return AIAskResult(
            intent="general_question",
            symbols=[],
            needs_data=False,
            response=FALLBACK_RESPONSES["ai_error"]
        ).dict()

    def _contains_placeholders(self, text: str) -> bool:
        """Check if text contains template placeholders like {symbol1}, {price1}, etc."""
        if not isinstance(text, str):
            return False
        placeholder_pattern = r'\{[a-zA-Z_][a-zA-Z0-9_]*\}'
        return bool(re.search(placeholder_pattern, text))

    async def generate_response(self, prompt: str) -> Optional[str]:
        """
        Generate a response using the AI model.
        
        Args:
            prompt: The prompt to send to the AI model
            
        Returns:
            The AI-generated response text or None if failed
        """
        try:
            if self.client is None:
                logger.warning(f"[{self.pipeline_id}] AI client is not configured (missing API key).")
                return None

            # Check circuit breaker
            if not await self.circuit_breaker.can_execute():
                logger.warning(f"[{self.pipeline_id}] Circuit breaker is OPEN. Rejecting AI call.")
                return None

            messages = [
                {"role": "user", "content": prompt},
            ]

            logger.info(f"🤖 [{self.pipeline_id}] Calling AI model {self.model} with query: {prompt[:50]}...")

            # Apply timeout to the AI call with proper async handling
            try:
                # Create a task with timeout
                completion_task = asyncio.create_task(
                    self._async_chat_completion(messages)
                )
                completion = await asyncio.wait_for(completion_task, timeout=self.ai_timeout)
            except asyncio.TimeoutError:
                logger.error(f"⏰ [{self.pipeline_id}] AI call timed out after {self.ai_timeout} seconds")
                await self.circuit_breaker.failure()
                return None

            # Extract the response text
            response_text = completion.choices[0].message.content.strip()
            
            if response_text:
                logger.info(f"✅ [{self.pipeline_id}] AI call successful. Intent: general_question, Symbols: []")
                await self.circuit_breaker.success()
                return response_text
            else:
                logger.warning(f"⚠️ [{self.pipeline_id}] AI returned empty response")
                await self.circuit_breaker.failure()
                return None

        except Exception as e:
            logger.error(f"❌ [{self.pipeline_id}] Error generating AI response: {e}")
            await self.circuit_breaker.failure()
            return None

    def _extract_json_from_response(self, response: str) -> Union[Dict, None]:
        """Extract JSON data from AI response with enhanced robustness"""
        if not isinstance(response, str):
            return None

        # Strip markdown fences and other common AI response wrappers
        cleaned_response = re.sub(r'^.*?```json\s*', '', response, flags=re.DOTALL | re.IGNORECASE)
        cleaned_response = re.sub(r'```.*$', '', cleaned_response, flags=re.DOTALL | re.IGNORECASE)
        cleaned_response = cleaned_response.strip()

        try:
            return json.loads(cleaned_response)
        except json.JSONDecodeError:
            # Fallback for cases where JSON is not perfectly formed
            json_match = re.search(r'\{.*\}', cleaned_response, re.DOTALL)
            if json_match:
                try:
                    return json.loads(json_match.group(0))
                except json.JSONDecodeError:
                    pass
        return None