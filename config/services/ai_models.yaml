# AI Model Configuration for TradingView Automation
# ================================================
# This file defines optimal model selection for different task types
# Based on performance analysis and cost optimization

# Model Definitions
# Each model has specific strengths for different use cases
models:
  # QUICK MODELS - Fast, efficient for simple tasks
  quick:
    model_id: "moonshotai/kimi-k2-0905"  # Fast model via OpenRouter
    max_tokens: 2000
    temperature: 0.1  # Low temperature for consistent extraction
    cost_per_1k_tokens: 0.002
    accuracy_rating: 0.90
    avg_response_time_ms: 400
    description: "Fast model for symbol extraction, intent classification, JSON parsing"
    use_cases:
      - symbol_extraction
      - intent_classification
      - simple_validation
      - quick_formatting
      - json_extraction
    
  # ANALYSIS MODELS - Balanced performance for user-facing responses
  analysis:
    model_id: "anthropic/claude-3.5-sonnet"  # Reliable, good reasoning
    max_tokens: 4000
    temperature: 0.3  # Moderate creativity for analysis
    cost_per_1k_tokens: 0.003
    accuracy_rating: 0.95
    avg_response_time_ms: 2000
    description: "Balanced model for market analysis and user-facing responses"
    use_cases:
      - market_analysis
      - technical_analysis
      - sentiment_analysis
      - price_prediction
      - user_explanations
      - report_generation
    
  # HEAVY MODELS - Premium intelligence for critical decisions
  heavy:
    model_id: "anthropic/claude-3.5-sonnet"  # High capability for complex reasoning
    max_tokens: 8000
    temperature: 0.2  # Low temperature for precise reasoning
    cost_per_1k_tokens: 0.003
    accuracy_rating: 0.98
    avg_response_time_ms: 4000
    description: "Premium model for complex reasoning and critical decisions"
    use_cases:
      - complex_reasoning
      - risk_assessment
      - portfolio_optimization
      - regulatory_compliance
      - multi_step_analysis
      - critical_decisions
    
  # FALLBACK MODEL - Reliable backup when primary models fail
  fallback:
    model_id: "moonshotai/kimi-k2-0905"  # Reliable fallback via OpenRouter
    max_tokens: 2000
    temperature: 0.5
    cost_per_1k_tokens: 0.0
    accuracy_rating: 0.80
    avg_response_time_ms: 1000
    description: "Reliable fallback model for when primary models are unavailable"

  # SPECIALIZED MODELS - All accessed via OpenRouter
  tongyi_deepresearch:
    model_id: "alibaba/tongyi-deepresearch-30b-a3b"  # Via OpenRouter
    max_tokens: 4000
    temperature: 0.3
    cost_per_1k_tokens: 0.002
    accuracy_rating: 0.90
    avg_response_time_ms: 3000
    description: "Alibaba's deep research model for complex analysis (via OpenRouter)"
    use_cases:
      - market_analysis
      - technical_analysis
      - fundamental_analysis
      - complex_reasoning
      - research_tasks

  internvl3:
    model_id: "opengvlab/internvl3-78b"  # Via OpenRouter
    max_tokens: 6000
    temperature: 0.2
    cost_per_1k_tokens: 0.004
    accuracy_rating: 0.92
    avg_response_time_ms: 4000
    description: "OpenGVLab's vision-language model for multimodal analysis (via OpenRouter)"
    use_cases:
      - technical_analysis
      - chart_analysis
      - visual_data_processing
      - complex_analysis
      - multimodal_tasks

  kimi_k2_0905:
    model_id: "moonshotai/kimi-k2-0905"  # Via OpenRouter
    max_tokens: 4000
    temperature: 0.3
    cost_per_1k_tokens: 0.003
    accuracy_rating: 0.88
    avg_response_time_ms: 2500
    description: "Updated Kimi model with improved performance (via OpenRouter)"
    use_cases:
      - market_analysis
      - technical_analysis
      - sentiment_analysis
      - user_explanations
      - general_analysis

  qwen_plus_thinking:
    model_id: "qwen/qwen-plus-2025-07-28:thinking"  # Via OpenRouter
    max_tokens: 8000
    temperature: 0.1
    cost_per_1k_tokens: 0.005
    accuracy_rating: 0.95
    avg_response_time_ms: 5000
    description: "Qwen Plus with thinking capabilities for complex reasoning (via OpenRouter)"
    use_cases:
      - complex_reasoning
      - multi_step_analysis
      - risk_assessment
      - portfolio_optimization
      - critical_decisions

  cogito_v2:
    model_id: "deepcogito/cogito-v2-preview-deepseek-671b"  # Via OpenRouter
    max_tokens: 10000
    temperature: 0.2
    cost_per_1k_tokens: 0.008
    accuracy_rating: 0.96
    avg_response_time_ms: 6000
    description: "DeepCogito's advanced reasoning model for financial analysis (via OpenRouter)"
    use_cases:
      - complex_reasoning
      - financial_modeling
      - risk_assessment
      - portfolio_optimization
      - regulatory_compliance
      - critical_decisions

# Task-specific routing rules
# Maps specific tasks to optimal models with priority preferences
task_routing:
  # Quick tasks - prioritize speed and cost
  symbol_extraction:
    preferred_model: "quick"
    priority: "speed"
    max_cost_per_1k: 0.001
    
  intent_classification:
    preferred_model: "analysis"
    priority: "accuracy"
    max_cost_per_1k: 0.005
    
  simple_validation:
    preferred_model: "quick"
    priority: "cost"
    
  quick_formatting:
    preferred_model: "quick"
    priority: "speed"
    
  # Analysis tasks - balanced approach with specialized models (all via OpenRouter)
  market_analysis:
    preferred_model: "tongyi_deepresearch"
    fallback_models: ["analysis", "kimi_k2_0905"]
    priority: "balanced"
    max_cost_per_1k: 0.005

  technical_analysis:
    preferred_model: "internvl3"
    fallback_models: ["analysis", "kimi_k2_0905"]
    priority: "accuracy"
    max_cost_per_1k: 0.005

  sentiment_analysis:
    preferred_model: "kimi_k2_0905"
    fallback_models: ["analysis", "tongyi_deepresearch"]
    priority: "balanced"

  price_prediction:
    preferred_model: "qwen_plus_thinking"
    fallback_models: ["analysis", "cogito_v2"]
    priority: "accuracy"

  # Heavy tasks - prioritize accuracy with specialized models (all via OpenRouter)
  complex_reasoning:
    preferred_model: "qwen_plus_thinking"
    fallback_models: ["cogito_v2", "heavy"]
    priority: "accuracy"
    max_cost_per_1k: 0.01

  risk_assessment:
    preferred_model: "cogito_v2"
    fallback_models: ["qwen_plus_thinking", "heavy"]
    priority: "accuracy"
    max_cost_per_1k: 0.015

  portfolio_optimization:
    preferred_model: "cogito_v2"
    fallback_models: ["qwen_plus_thinking", "heavy"]
    priority: "accuracy"
    max_cost_per_1k: 0.02

  regulatory_compliance:
    preferred_model: "cogito_v2"
    fallback_models: ["qwen_plus_thinking", "heavy"]
    priority: "accuracy"
    max_cost_per_1k: 0.02

# Provider-specific configurations
providers:
  openrouter:
    base_url: "https://openrouter.ai/api/v1"
    retry_attempts: 3
    timeout_seconds: 30
    rate_limit_buffer: 0.1  # 10% buffer for rate limits
    
  anthropic:
    retry_attempts: 2
    timeout_seconds: 45
    
  google:
    retry_attempts: 3
    timeout_seconds: 20

# Performance monitoring and optimization
performance:
  # Track model performance metrics
  enable_metrics: true
  metrics_retention_days: 30
  
  # Circuit breaker configuration
  circuit_breaker:
    failure_threshold: 5
    timeout_seconds: 300
    half_open_max_calls: 3
    
  # Auto-optimization settings
  auto_optimization:
    enabled: true
    min_samples: 100  # Minimum calls before optimization
    performance_weight: 0.4
    cost_weight: 0.3
    speed_weight: 0.3

# Caching configuration for different query types
caching:
  # Live price queries - short cache for real-time data
  live_price_queries:
    enabled: true
    ttl_seconds: 3
    intents: ["price_check", "current_price", "live_quote"]
    bypass_conditions:
      force_fresh: false
      
  # Analysis results - longer cache for complex analysis
  analysis_results:
    enabled: true
    ttl_seconds: 180
    intents: ["technical_analysis", "market_analysis", "sentiment_analysis"]
    
  # General queries - standard caching
  general_queries:
    enabled: true
    ttl_seconds: 300
    
# Cost management and budgeting
cost_management:
  # Daily budget limits (USD)
  daily_budget:
    quick_models: 5.0
    analysis_models: 20.0
    heavy_models: 50.0
    total: 75.0
    
  # Alert thresholds
  alerts:
    budget_warning_threshold: 0.8  # 80% of budget
    high_cost_query_threshold: 0.10  # $0.10 per query
    
  # Cost optimization rules
  optimization:
    auto_downgrade_on_budget: true
    prefer_free_models_when_possible: false
    batch_similar_queries: true

# Environment-specific overrides
# All models accessed via OpenRouter
environments:
  development:
    # Use experimental models in development (all via OpenRouter)
    models:
      quick:
        model_id: "moonshotai/kimi-k2-0905"  # Via OpenRouter
      analysis:
        model_id: "alibaba/tongyi-deepresearch-30b-a3b"  # Via OpenRouter
      heavy:
        model_id: "qwen/qwen-plus-2025-07-28:thinking"  # Via OpenRouter

  production:
    # Use optimized models in production (all via OpenRouter)
    models:
      quick:
        model_id: "moonshotai/kimi-k2-0905"  # Via OpenRouter
      analysis:
        model_id: "alibaba/tongyi-deepresearch-30b-a3b"  # Via OpenRouter
      heavy:
        model_id: "deepcogito/cogito-v2-preview-deepseek-671b"  # Via OpenRouter

  testing:
    # Use consistent, fast models for testing (all via OpenRouter)
    models:
      quick:
        model_id: "moonshotai/kimi-k2-0905"  # Via OpenRouter
      analysis:
        model_id: "alibaba/tongyi-deepresearch-30b-a3b"  # Via OpenRouter
      heavy:
        model_id: "qwen/qwen-plus-2025-07-28:thinking"  # Via OpenRouter
