#!/usr/bin/env python3
"""
Test the pipeline with a serious market question and evaluate response quality
"""

import asyncio
import sys
sys.path.append('.')

from src.shared.ai_services.enhanced_intent_detector import EnhancedIntentDetector
from src.shared.data_providers.aggregator import DataProviderAggregator
from src.shared.technical_analysis.enhanced_calculator import EnhancedTechnicalCalculator
from src.shared.ai_chat.ai_client import AIClient
from src.shared.ai_services.fact_verifier import TradingFactVerifier

async def test_serious_question():
    print('🔍 Testing Serious Market Question')
    print('=' * 60)
    
    # Serious market question that a real trader might ask
    query = 'NVDA has dropped 15% this week. Given the current AI chip market conditions and upcoming earnings, should I consider this a buying opportunity or wait for further decline? What do the technical indicators suggest about momentum and support levels?'
    
    print(f'📝 Question: {query}')
    print()
    
    # Initialize components
    intent_detector = EnhancedIntentDetector()
    data_aggregator = DataAggregator()
    tech_calculator = EnhancedTechnicalCalculator()
    ai_client = AIClient()
    fact_verifier = TradingFactVerifier()
    
    # Step 1: Intent Detection
    print('1️⃣ Intent Detection...')
    analysis = await intent_detector.analyze_query(query)
    print(f'   Intent: {analysis.primary_intent.value}')
    print(f'   Symbols: {analysis.extracted_symbols}')
    print(f'   Confidence: {analysis.confidence}')
    print()
    
    # Step 2: Data Fetching
    print('2️⃣ Data Fetching...')
    data_results = {}
    for symbol in analysis.extracted_symbols:
        try:
            result = await data_aggregator.get_current_price(symbol)
            if result and result.get('success'):
                data_results[symbol] = result['data']
                print(f'   ✅ {symbol}: ${result["data"]["price"]}')
            else:
                print(f'   ❌ {symbol}: Failed to fetch data')
        except Exception as e:
            print(f'   ❌ {symbol}: Error - {e}')
    print()
    
    # Step 3: Technical Analysis
    print('3️⃣ Technical Analysis...')
    tech_results = {}
    for symbol in analysis.extracted_symbols:
        try:
            hist_data = await data_aggregator.get_history(symbol, period='1mo')
            if hist_data and hist_data.get('success'):
                indicators_result = await tech_calculator.calculate_indicators_from_historical_data(hist_data['data'])
                if indicators_result.indicators:
                    tech_results[symbol] = indicators_result.indicators
                    print(f'   ✅ {symbol}: {len(indicators_result.indicators)} indicators calculated')
                else:
                    print(f'   ⚠️ {symbol}: No indicators calculated')
            else:
                print(f'   ⚠️ {symbol}: No historical data available')
        except Exception as e:
            print(f'   ❌ {symbol}: Error - {e}')
    print()
    
    # Step 4: AI Analysis
    print('4️⃣ AI Analysis Generation...')
    
    # Create comprehensive prompt
    prompt = f'''You are a professional trading analyst. A trader is asking a serious question about market conditions.

QUERY: {query}

AVAILABLE DATA:
- Intent: {analysis.primary_intent.value}
- Symbols: {analysis.extracted_symbols}
- Market Data: {data_results}
- Technical Indicators: {len(tech_results.get('NVDA', {}))} indicators available for NVDA

CRITICAL INSTRUCTIONS:
1. Only use the real data provided above
2. Be honest about data limitations
3. Provide actionable insights based on available data
4. Include appropriate risk disclaimers
5. Address the specific market context mentioned (AI chip market, earnings)
6. Discuss technical momentum and support levels if data is available

Provide a professional, comprehensive analysis that a real trader would find valuable.'''

    try:
        response = await ai_client.generate_response(prompt)
        if response:
            print(f'   ✅ Response Generated ({len(response)} characters)')
            print()
            print('📊 AI RESPONSE:')
            print('=' * 60)
            print(response)
            print('=' * 60)
            print()
            
            # Step 5: Fact Verification
            print('5️⃣ Fact Verification...')
            fact_check = fact_verifier.verify_facts(response, data_results, analysis.extracted_symbols)
            print(f'   Valid: {fact_check.is_valid}')
            print(f'   Confidence: {fact_check.confidence:.2f}')
            print(f'   Issues: {len(fact_check.issues_found)}')
            if fact_check.issues_found:
                for issue in fact_check.issues_found[:3]:
                    print(f'     - {issue}')
            
            return response, fact_check
        else:
            print('   ❌ Failed to generate response')
            return None, None
    except Exception as e:
        print(f'   ❌ Error: {e}')
        return None, None

if __name__ == "__main__":
    # Run the test
    response, fact_check = asyncio.run(test_serious_question())
